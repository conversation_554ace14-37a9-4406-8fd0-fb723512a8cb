import { Job as BullJob, Queue, Worker } from 'bullmq';
import { createRedisConnection } from '../config/redis';
import JobSimulationGenerationModel from '../models/JobSimulationGeneration';
import { processJobSimulation } from '../services/simulationServices/simulationService';
import { sendWebhook } from '../services/webhookService';
import logger from '../services/logger';

export interface JobData {
  jobId: string;
}

// Create Redis connection
const connection = createRedisConnection();

const connectionOptions = {
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6349'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '2'),
};

// Create the queue
export const jobQueue = new Queue<JobData>(
  process.env.QUEUE_NAME || 'job-simulation-generation-queue',
  {
    connection: connectionOptions
  }
);

// Create the worker with concurrency limit
export const createWorker = (): Worker<JobData> => {
  const maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_JOBS || '2');

  const worker = new Worker<JobData>(
    process.env.QUEUE_NAME || 'job-simulation-generation-queue',
    async (job: BullJob<JobData>) => {
      const { jobId } = job.data;

      try {
        logger.simple(`Job Processing ::: `, jobId, 'Starting');

        // Find the job in database
        const jobDoc = await JobSimulationGenerationModel.findOne({ jobId: jobId });
        if (!jobDoc) {
          throw new Error(`Job not found: ${jobId}`);
        }

        // Update status to processing
        await JobSimulationGenerationModel.updateOne(
          { jobId: jobId },
          {
            simulationStatus: 'processing',
            updatedAt: new Date()
          }
        );

        // Process the simulation
        const simulationResult = await processJobSimulation(jobDoc);

        // Update job based on simulation result
        const updateData: any = {
          simulationStatus: simulationResult.success ? 'completed' : 'failed',
          processedAt: new Date(),
          updatedAt: new Date()
        };

        if (simulationResult.success) {
          updateData.simulationData = simulationResult.data;
        } else {
          updateData.errorMessage = simulationResult.error;
        }

        const updatedJob = await JobSimulationGenerationModel.findOneAndUpdate({ jobId: jobId }, updateData);

        if (!updatedJob) {
          throw new Error(`Updated job not found: ${jobId}`);
        }

        // Send webhook notification
        logger.simple(`Job Processing ::: `, jobId, 'Sending webhook');
        const webhookResult = await sendWebhook(updatedJob);

        // Update webhook status
        await JobSimulationGenerationModel.findOneAndUpdate(
          { jobId: jobId },
          {
            webhookStatus: webhookResult.success ? 'success' : 'failed',
            webhookRetryCount: webhookResult.retryCount,
            webhookSentAt: new Date(),
            updatedAt: new Date()
          }
        );

        logger.simple(`Job Processing ::: `, jobId, '✅ completed');
        return {
          jobId,
          simulationSuccess: simulationResult.success,
          webhookSuccess: webhookResult.success
        };

      } catch (error) {
        logger.simple(`Job Processing ::: `, jobId, '❌ Failed', error);

        // Update job status to failed
        try {
          await JobSimulationGenerationModel.updateOne(
            { jobId: jobId },
            {
              simulationStatus: 'failed',
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              processedAt: new Date(),
              updatedAt: new Date()
            }
          );
        } catch (error) { logger.simple(`Job Processing ::: `, jobId, '❌ Failed to update job status', error); }

        throw error;
      }
    },
    {
      connection: connectionOptions,
      concurrency: maxConcurrentJobs,
      removeOnComplete: { count: 100 }, // Keep last 100 completed jobs
      removeOnFail: { count: 50 }       // Keep last 50 failed jobs
    }
  );

  // Worker event handlers
  worker.on('completed', (job) => {
    logger.simple(`✅ Worker completed job ${job.id} with data:`, job.returnvalue);
  });

  worker.on('failed', (job, err) => {
    logger.simple(`❌ Worker failed job ${job?.id}:`, err);
  });

  worker.on('error', (err) => {
    logger.simple('❌ Worker error:', err);
  });

  return worker;
};

// Add job to queue
export const addJobToQueue = async (jobId: string): Promise<void> => {
  try {
    await jobQueue.add(
      'process-simulation',
      { jobId },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: true,
        removeOnFail: false
      }
    );

    logger.simple(`Job added to queue: ${jobId}`);
  } catch (error) {
    logger.simple(`Failed to add job to queue: ${jobId}`, error);
    throw error;
  }
};
