import { Router, Request, Response } from 'express';
import JobSimulationGenerationModel from '../models/JobSimulationGeneration';
import { jobQueue } from '../queue/jobQueue';
import logger from '../services/logger';

const router = Router();

// GET /api/stats - Get system statistics
router.get('/', async (req: Request, res: Response) => {
  try {
    // Job statistics
    const [
      totalJobs,
      pendingJobs,
      processingJobs,
      completedJobs,
      failedJobs,
      webhookSuccessJobs,
      webhookFailedJobs,
      webhookPendingJobs
    ] = await Promise.all([
      JobSimulationGenerationModel.countDocuments(),
      JobSimulationGenerationModel.countDocuments({ simulationStatus: 'pending' }),
      JobSimulationGenerationModel.countDocuments({ simulationStatus: 'processing' }),
      JobSimulationGenerationModel.countDocuments({ simulationStatus: 'completed' }),
      JobSimulationGenerationModel.countDocuments({ simulationStatus: 'failed' }),
      JobSimulationGenerationModel.countDocuments({ webhookStatus: 'success' }),
      JobSimulationGenerationModel.countDocuments({ webhookStatus: 'failed' }),
      JobSimulationGenerationModel.countDocuments({ webhookStatus: 'pending' })
    ]);

    // Queue statistics
    const queueStats = await Promise.all([
      jobQueue.getWaiting(),
      jobQueue.getActive(),
      jobQueue.getCompleted(),
      jobQueue.getFailed()
    ]);

    const [waitingJobs, activeJobs, completedQueueJobs, failedQueueJobs] = queueStats;

    // Recent activity (last 24 hours)
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentActivity = await JobSimulationGenerationModel.aggregate([
      {
        $match: {
          createdAt: { $gte: last24Hours }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d %H:00",
              date: "$createdAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Success rate calculation
    const totalProcessed = completedJobs + failedJobs;
    const successRate = totalProcessed > 0 ? (completedJobs / totalProcessed * 100).toFixed(2) : '0.00';
    
    const webhookTotal = webhookSuccessJobs + webhookFailedJobs;
    const webhookSuccessRate = webhookTotal > 0 ? (webhookSuccessJobs / webhookTotal * 100).toFixed(2) : '0.00';

    res.status(200).json({
      timestamp: new Date().toISOString(),
      jobs: {
        total: totalJobs,
        pending: pendingJobs,
        processing: processingJobs,
        completed: completedJobs,
        failed: failedJobs,
        successRate: `${successRate}%`
      },
      webhooks: {
        success: webhookSuccessJobs,
        failed: webhookFailedJobs,
        pending: webhookPendingJobs,
        successRate: `${webhookSuccessRate}%`
      },
      queue: {
        waiting: waitingJobs.length,
        active: activeJobs.length,
        completed: completedQueueJobs.length,
        failed: failedQueueJobs.length,
        maxConcurrency: parseInt(process.env.MAX_CONCURRENT_JOBS || '2')
      },
      recentActivity: recentActivity.map(item => ({
        hour: item._id,
        jobsCreated: item.count
      }))
    });

  } catch (error) {
    logger.simple('❌ Error fetching stats:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch statistics'
    });
  }
});

// GET /api/stats/health - Detailed health check
router.get('/health', async (req: Request, res: Response) => {
  try {
    const checks = {
      database: false,
      queue: false,
      redis: false
    };

    // Check database
    try {
      await JobSimulationGenerationModel.findOne().limit(1);
      checks.database = true;
    } catch (error) {
      logger.simple('Database health check failed:', error);
    }

    // Check queue
    try {
      await jobQueue.getWaiting();
      checks.queue = true;
    } catch (error) {
      logger.simple('Queue health check failed:', error);
    }

    // Check Redis (through queue connection)
    try {
      const queueHealth = await jobQueue.getWaiting();
      checks.redis = true;
    } catch (error) {
      logger.simple('Redis health check failed:', error);
    }

    const allHealthy = Object.values(checks).every(check => check === true);
    const status = allHealthy ? 'healthy' : 'unhealthy';

    res.status(allHealthy ? 200 : 503).json({
      status,
      timestamp: new Date().toISOString(),
      checks,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0'
    });

  } catch (error) {
    logger.simple('❌ Error in health check:', error);
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

export default router;
