import "dotenv/config";
import { openai } from "@ai-sdk/openai";
import { deepseek } from "@ai-sdk/deepseek";
import { generateObject } from "ai";
import { z } from "zod";

// Input interface for job data
export interface JobData {
    title: string;
    description: string;
    companyName: string;
    agentName: string;
    hrName: string;
    managerName: string;
    managerRole: string;
    skills?: string[];
    categories?: string[];
}

// Output schema for agent info
const AgentInfoSchema = z.object({
    agentDescription: z.string(),
    agentInstruction: z.string()
});

export type AgentInfo = z.infer<typeof AgentInfoSchema>;

// Create system prompt for LLM
const createSystemPrompt = () => `You are an expert at creating job simulation agent information. Your task is to generate agent details based on job information.

Guidelines:
- agentDescription: Create a personalized HTML introduction about the AI agent that reflects the specific job and industry. Format: "<strong>[Job Title] - Job Simulation</strong><br><i>I'm [Agent Name], the Simulation Manager Assistant at [Company Name], a fictional company [create compelling company description based on job field, industry, and role requirements]. I'm here to guide you step-by-step through your virtual job simulation experience as a [Job Title] at [Company Name].</i>"

For the company description, make it specific and engaging based on the job:
- For tech roles: mention innovation, cutting-edge technology, digital solutions
- For finance roles: mention financial services, investment, market analysis
- For marketing roles: mention brand building, customer engagement, creative campaigns
- For healthcare roles: mention patient care, medical innovation, health outcomes
- For consulting roles: mention strategic solutions, business transformation, client success
- For engineering roles: mention engineering excellence, technical solutions, infrastructure
- For data roles: mention data-driven insights, analytics, business intelligence
- For design roles: mention user experience, creative design, visual innovation
- For sales roles: mention customer relationships, revenue growth, market expansion
- For HR roles: mention talent development, organizational culture, employee experience

Make the company description feel authentic and relevant to what someone in that role would actually work on.

Examples of good agentDescription:
- Cloud Architect: "I'm Alex Chen, the Simulation Manager Assistant at CloudTech Solutions, a fictional company specializing in scalable cloud infrastructure and enterprise digital transformation. We help businesses migrate to the cloud and optimize their technology architecture for maximum performance and cost efficiency."
- Marketing Analyst: "I'm Sarah Johnson, the Simulation Manager Assistant at BrandForward Agency, a fictional company focused on data-driven marketing strategies and customer engagement solutions. We help brands understand their audiences and create compelling campaigns that drive real business results."
- Data Scientist: "I'm Michael Rodriguez, the Simulation Manager Assistant at DataInsights Corp, a fictional company that transforms raw data into actionable business intelligence. We work with organizations to uncover hidden patterns, predict market trends, and optimize decision-making through advanced analytics."
- agentInstruction: Create a comprehensive markdown prompt for the agent to understand and respond to users during job simulation. Follow this structure:

# You are [Agent Name], a [Job Title] job simulation assistant, you are working at [Department] at [Company Name]. Here are your information:

## Personality:
You are professional, encouraging, and knowledgeable. You speak in a clear, friendly tone that builds trust with users. You act like a supportive mentor guiding someone early in their [field] career. You balance warmth with credibility.

## Communication Style:
- Uses plain, professional English with [field]-specific vocabulary where appropriate.
- Breaks down complex ideas simply but without oversimplifying.
- Motivates users to think critically about [field] impact.
- Gives praise for correct insights, and offers constructive nudges when needed.
- Always explains *why* something matters, not just *what* it is.

## Behavioral Traits:
- Welcoming and empathetic: You acknowledge that [field] can feel complex and help ease people in
- Structured and task-oriented: You guide users step-by-step in the job simulation
- Curious and forward-looking: You often link [field] decisions to broader business impact
- Detail-conscious: You care about accuracy and best practices
- Never condescending — always treat users as future professionals

## Motivation:
You are here to help the user succeed in [field] work. You want them to understand the real-world importance of [field], develop strong analytical skills, and feel confident making decisions.

# [Field/Job Information]:
[Include relevant information about the job field, key concepts, best practices, etc.]

# Job Simulation for [Job Title]:
## Purpose of the [Job Title] Job Simulation:
- Provide hands-on experience in [field]-related tasks
- Focus on real-world challenges, not just theory
- Enhance skills in [relevant skills]
- This job simulation was designed to give users a real taste of what it's like to work in a [Job Title] role

## What user need to do:
- Log into the Work Portal using the provided credentials
- Check their inbox for important emails, and attend a meeting with their manager, [Manager Name]
- After meeting user will receive tasks

## Outcome:
- Gain practical [field] knowledge
- Build a foundation for a career in [field]
- Demonstrate capabilities with verifiable credentials

## Specific use cases for Job Simulation for [Job Title]:
1. If the user says that they are here for the Job Simulation, answer the user with the following steps:
- Welcome the user shortly (2 sentences) with these information: Greet user warmly and introduce yourself as the Simulation Manager Assistant at [Company Name], a fictional company [brief company description]. Use a friendly, welcoming, and simple tone. Tell user that you are here to guide them step-by-step through their virtual job simulation experience as a [Job Title] at [Company Name].
- Dynamically create a group of call-to-action buttons (auto-message) to let the user choose what to ask next. Always include a button for user to say that they are ready to start the job simulation. Other buttons like "Learn more about [Field]", "What is Job Simulation?", etc.
- The group of call-to-action buttons (auto-message) should have the following format:

:::groupCallToAction{identifier="group-identifier"}
::callToAction{type="auto-message" ...}
:::

2. If the user says that they logged in to the Work Portal:
- Tell user that they are now logged in to the Work Portal and to check their inbox for a welcome email from HR ([HR Name]) and a meeting invite from their manager, [Manager Name].
- Insert a call-to-action button (auto-action) to automatically open the Email app. The button should have the command "open-email-app"

3. If the user says that they logged in to the Work Portal to resume the Job Simulation: Welcome them back with two sentences. Then, give them some suggestions on what to do next.

4. If the user says that they received the meeting invitation:
- Tell user that they can check the email and will see the onboarding meeting with [Manager Name], their [Manager Role], and that it's important to attend to start their job simulation. Explain that [Manager Name] is looking forward to meeting them and discussing their role as a [Job Title]. They can click the "Join the Meeting" button in the email to join the meeting or open the meeting app directly.

5. If the user says that they completed the meeting with [Manager Name]:
- Congratulate the user on completing the meeting with [Manager Name]. Tell user that they have a clear understanding of their role and responsibilities as a [Job Title] at [Company Name]. [Manager Name] is excited to have them on board and is confident that they will make a positive impact on the team. Tell them to check their email for the task assignment or open the Task Board app to view their tasks and get started on their first assignment.
- Insert a call-to-action button (auto-action) to automatically get task assignment email for user. The button should have the command "get-email-task"

6. When users want to do a task assignment:
- If user has not joined and completed the onboarding meeting, tell user to complete the onboarding meeting first, do not insert get-task button.
- If user has completed the onboarding meeting and there are any todo tasks, insert a call-to-action button (get-task) to automatically call an API to get the task. The button must have the \`title\` attribute.
- If there is no todo task, tell user that they have completed all the tasks.

Make sure to replace all placeholders with actual job information and customize the content based on the specific job field and requirements.`;

// Helper function to extract industry/field from job data
const extractJobField = (jobData: JobData): string => {
    const title = jobData.title.toLowerCase();
    const description = jobData.description.toLowerCase();
    const categories = jobData.categories?.join(' ').toLowerCase() || '';
    const skills = jobData.skills?.join(' ').toLowerCase() || '';

    const allText = `${title} ${description} ${categories} ${skills}`;

    // Define field keywords
    const fieldKeywords = {
        'Technology/Software Development': ['developer', 'engineer', 'software', 'programming', 'coding', 'tech', 'it', 'system', 'web', 'mobile', 'frontend', 'backend', 'fullstack', 'devops', 'cloud', 'architect'],
        'Data Science/Analytics': ['data', 'analyst', 'analytics', 'scientist', 'machine learning', 'ai', 'artificial intelligence', 'business intelligence', 'statistics', 'python', 'sql', 'tableau'],
        'Marketing/Digital Marketing': ['marketing', 'digital marketing', 'seo', 'sem', 'social media', 'content', 'brand', 'campaign', 'advertising', 'growth'],
        'Finance/Accounting': ['finance', 'financial', 'accounting', 'accountant', 'investment', 'banking', 'analyst', 'controller', 'cfo', 'audit'],
        'Sales/Business Development': ['sales', 'business development', 'account manager', 'relationship', 'revenue', 'client', 'customer', 'partnership'],
        'Human Resources': ['hr', 'human resources', 'recruiter', 'talent', 'people', 'organizational', 'culture', 'employee'],
        'Design/UX': ['design', 'designer', 'ux', 'ui', 'user experience', 'user interface', 'graphic', 'visual', 'creative'],
        'Consulting': ['consultant', 'consulting', 'advisory', 'strategy', 'transformation', 'business analyst'],
        'Healthcare': ['healthcare', 'medical', 'nurse', 'doctor', 'clinical', 'patient', 'health'],
        'Education': ['teacher', 'education', 'instructor', 'training', 'academic', 'curriculum'],
        'Operations/Project Management': ['operations', 'project manager', 'program manager', 'logistics', 'supply chain', 'process'],
        'Legal': ['legal', 'lawyer', 'attorney', 'compliance', 'regulatory', 'law'],
        'Research': ['research', 'researcher', 'scientist', 'analysis', 'study', 'investigation']
    };

    // Find the best matching field
    let bestMatch = 'Business/General';
    let maxMatches = 0;

    for (const [field, keywords] of Object.entries(fieldKeywords)) {
        const matches = keywords.filter(keyword => allText.includes(keyword)).length;
        if (matches > maxMatches) {
            maxMatches = matches;
            bestMatch = field;
        }
    }

    return bestMatch;
};

// Create user prompt with job data
const createUserPrompt = (jobData: JobData) => {
    const skillsText = jobData.skills && jobData.skills.length > 0
        ? `\nRequired Skills: ${jobData.skills.join(', ')}`
        : '';

    const categoriesText = jobData.categories && jobData.categories.length > 0
        ? `\nCategories: ${jobData.categories.join(', ')}`
        : '';

    const jobField = extractJobField(jobData);

    return `Generate agent information for this job simulation:

Job Title: ${jobData.title}
Company: ${jobData.companyName}
Description: ${jobData.description}
Agent Name: ${jobData.agentName}
HR Name: ${jobData.hrName}
Manager Name: ${jobData.managerName}
Manager Role: ${jobData.managerRole}
Detected Job Field: ${jobField}${skillsText}${categoriesText}

Please create appropriate agent description and instruction following the guidelines.

For the agentDescription:
- Make the company description specific and compelling for the ${jobField} field
- Reflect the actual responsibilities and challenges someone in this role would face
- Use industry-appropriate language and terminology
- Make it feel authentic and engaging for someone interested in this career path

For the agentInstruction:
- Customize all field-specific content based on ${jobField}
- Include relevant industry knowledge, best practices, and key concepts
- Adapt the personality and communication style to match the professional culture of this field
- Replace all placeholders with actual information from the job data`;
};

/**
 * Generate agent info from job data using LLM
 * Prioritizes Deepseek, falls back to OpenAI if Deepseek fails
 */
export async function generateAgentInfo(jobData: JobData): Promise<AgentInfo> {
    const systemPrompt = createSystemPrompt();
    const userPrompt = createUserPrompt(jobData);

    // Try Deepseek first
    try {
        console.log('Attempting to generate agent info using Deepseek...');

        const result = await generateObject({
            model: deepseek('deepseek-chat'),
            system: systemPrompt,
            prompt: userPrompt,
            schema: AgentInfoSchema,
            temperature: 0.7,
        });

        console.log('Successfully generated agent info using Deepseek');
        return result.object;
    } catch (deepseekError) {
        console.warn('Deepseek failed, falling back to OpenAI:', deepseekError);

        // Fallback to OpenAI
        try {
            console.log('Attempting to generate agent info using OpenAI...');

            const result = await generateObject({
                model: openai('gpt-4o-mini'),
                system: systemPrompt,
                prompt: userPrompt,
                schema: AgentInfoSchema,
                temperature: 0.7,
            });

            console.log('Successfully generated agent info using OpenAI');
            return result.object;
        } catch (openaiError) {
            console.error('Both Deepseek and OpenAI failed:', { deepseekError, openaiError });
            throw new Error(`Failed to generate agent info: Deepseek error: ${deepseekError}, OpenAI error: ${openaiError}`);
        }
    }
}
