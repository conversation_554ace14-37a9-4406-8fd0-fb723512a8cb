# Job Simulation Generation Engine

A service that generates job simulations using Redis, BullMQ, and MongoDB.

## Prerequisites

- Node.js
- MongoDB
- Redis

## Installation

1. Clone the repository and navigate to the project directory:

```bash
cd jobsimulationgeneration
```

2. Install dependencies:

```bash
npm install
```

3. Build the project:

```bash
npm run build
```

## Usage

### Development Mode

```bash
npm run dev
```

### Production Mode

```bash
npm run build
npm start
```

## Monitoring

```http
GET /health
```

## Job Processing Flow

1. **Job Submission**: Service A submits jobs via POST /api/jobs
2. **Queue Processing**: Jobs are added to BullMQ queue for background processing
3. **Simulation**: Each job is processed by the simulation service
4. **Webhook Notification**: Upon completion, a webhook is sent to Service A with retry logic
5. **Status Tracking**: All statuses are tracked in MongoDB

## Job States

### Simulation Status

- `pending`: Job is waiting to be processed
- `processing`: Job is currently being processed
- `completed`: Job simulation completed successfully
- `failed`: Job simulation failed

### Webhook Status

- `pending`: Webhook not yet sent
- `success`: Webhook sent successfully (HTTP 200)
- `failed`: Webhook failed after all retry attempts
