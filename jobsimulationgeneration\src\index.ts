import 'dotenv/config';
import app from './app';
import logger from './services/logger';


const PORT = process.env.PORT || 3002;

logger.simple("::: CHECK ENV ::: ", process.env.PORT);

app.listen(PORT, () => {
  logger.simple(`🚀 Job Simulation Generation Engine is running on port ${PORT}`);
  logger.simple(`Health check available at: http://localhost:${PORT}/health`);
  // logger.simple(`🌐 API documentation at: http://localhost:${PORT}/`);
});
