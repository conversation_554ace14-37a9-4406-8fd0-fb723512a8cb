import "dotenv/config";
import { openai } from "@ai-sdk/openai";
import { deepseek } from "@ai-sdk/deepseek";
import { generateObject } from "ai";
import { z } from "zod";

// Input interface for job data
export interface JobData {
    title: string;
    description: string;
    companyName: string;
    skills?: string[];
    categories?: string[];
}

// Output schema for simulation info
const SimulationInfoSchema = z.object({
    jobSimulationId: z.string(),
    name: z.string(),
    description: z.string(),
    username: z.string(),
    password: z.string()
});

export type SimulationInfo = z.infer<typeof SimulationInfoSchema>;

// Create system prompt for LLM
const createSystemPrompt = () => `You are an expert at creating job simulation information. Your task is to generate simulation details based on job information.

Guidelines:
- jobSimulationId: Create a short, memorable ID based on the job title. Use lowercase letters only, separate words with hyphens, no special characters or spaces. Keep it concise (2-4 words max).
- name: Create a clean, professional name for the simulation based on the job title, removing salary info and extra details.
- description: Write an engaging description that explains what users will experience in this simulation. Mention the company name, role responsibilities, and key skills they'll learn. Keep it informative but concise (2-3 sentences).
- username: Create a username for login using the format: [role].[company].sim - use lowercase, dots as separators, keep it short and memorable.
- password: Create a simple, memorable password combining role and year (e.g., "cloud2025"). Use lowercase letters and numbers only, no special characters.

Examples:
Input: "Cloud Architect (Up to $3500)" at "Nimbus Works"
Output:
- jobSimulationId: "cloud-architect"
- name: "Cloud Architect"
- description: "Work as a Cloud Architect at Nimbus Works, exploring how organizations design and manage cloud infrastructure. In this simulation, you'll gain insight into key concepts like scalability, security, and system architecture — helping you understand the role of a Cloud Architect in modern tech environments."
- username: "cloud.nimbuswork.sim"
- password: "cloud2025"`;

// Create user prompt with job data
const createUserPrompt = (jobData: JobData) => {
    const skillsText = jobData.skills && jobData.skills.length > 0
        ? `\nRequired Skills: ${jobData.skills.join(', ')}`
        : '';

    const categoriesText = jobData.categories && jobData.categories.length > 0
        ? `\nCategories: ${jobData.categories.join(', ')}`
        : '';

    return `Generate simulation information for this job:

Job Title: ${jobData.title}
Company: ${jobData.companyName}
Description: ${jobData.description}${skillsText}${categoriesText}

Please create appropriate simulation details following the guidelines.`;
};

/**
 * Generate simulation info from job data using LLM
 * Prioritizes Deepseek, falls back to OpenAI if Deepseek fails
 */
export async function generateSimulationInfo(jobData: JobData): Promise<SimulationInfo> {
    const systemPrompt = createSystemPrompt();
    const userPrompt = createUserPrompt(jobData);

    // Try Deepseek first
    try {
        console.log('Attempting to generate simulation info using Deepseek...');

        const result = await generateObject({
            model: deepseek('deepseek-chat'),
            system: systemPrompt,
            prompt: userPrompt,
            schema: SimulationInfoSchema,
            temperature: 0.5,
        });

        console.log('Successfully generated simulation info using Deepseek');
        return {
            ...result.object,
            jobSimulationId: `${result.object.jobSimulationId}-${Date.now()}`
        };
    } catch (deepseekError) {
        console.warn('Deepseek failed, falling back to OpenAI:', deepseekError);

        // Fallback to OpenAI
        try {
            console.log('Attempting to generate simulation info using OpenAI...');

            const result = await generateObject({
                model: openai('gpt-4o-mini'),
                system: systemPrompt,
                prompt: userPrompt,
                schema: SimulationInfoSchema,
                temperature: 0.5,
            });

            console.log('Successfully generated simulation info using OpenAI');
            return {
                ...result.object,
                jobSimulationId: `${result.object.jobSimulationId}-${Date.now()}`
            };
        } catch (openaiError) {
            console.error('Both Deepseek and OpenAI failed:', { deepseekError, openaiError });
            throw new Error(`Failed to generate simulation info: Deepseek error: ${deepseekError}, OpenAI error: ${openaiError}`);
        }
    }
}