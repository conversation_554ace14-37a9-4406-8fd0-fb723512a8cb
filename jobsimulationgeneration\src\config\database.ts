import mongoose from 'mongoose';
import logger from '../services/logger';

export const connectDatabase = async (): Promise<void> => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/jobsimulation';

    await mongoose.connect(mongoUri);

    logger.simple('✅ MongoDB connected successfully');

    // Handle connection events
    mongoose.connection.on('error', (error) => {
      logger.simple('❌ MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.simple('⚠️ MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      logger.simple('🔄 MongoDB reconnected');
    });

  } catch (error) {
    logger.simple('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
};

export const disconnectDatabase = async (): Promise<void> => {
  try {
    await mongoose.disconnect();
    logger.simple('✅ MongoDB disconnected successfully');
  } catch (error) {
    logger.simple('❌ Error disconnecting from MongoDB:', error);
  }
};
