import mongoose, { Document, Schema } from 'mongoose';

export interface IJobSimulationGeneration extends Document {
  jobId: string;
  title: string;
  description: string;
  skills?: string[];
  companyLogo: string;
  companyName: string;
  simulationStatus: 'pending' | 'processing' | 'completed' | 'failed';
  webhookStatus: 'pending' | 'success' | 'failed';
  webhookRetryCount: number;
  simulationData?: any;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
  webhookSentAt?: Date;
}

const JobSimulationGenerationSchema: Schema = new Schema({
  jobId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  skills: [{
    type: String,
    required: false,
    default: undefined
  }],
  companyLogo: {
    type: String,
    required: true
  },
  companyName: {
    type: String,
    required: true
  },
  simulationStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending',
    index: true
  },
  webhookStatus: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending',
    index: true
  },
  webhookRetryCount: {
    type: Number,
    default: 0
  },
  simulationData: {
    type: Schema.Types.Mixed,
    default: null
  },
  errorMessage: {
    type: String,
    default: null
  },
  processedAt: {
    type: Date,
    default: null
  },
  webhookSentAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Indexes for better query performance
JobSimulationGenerationSchema.index({ simulationStatus: 1, createdAt: 1 });
JobSimulationGenerationSchema.index({ webhookStatus: 1, webhookRetryCount: 1 });

export default mongoose.model<IJobSimulationGeneration>('jobSimulationGeneration', JobSimulationGenerationSchema);
