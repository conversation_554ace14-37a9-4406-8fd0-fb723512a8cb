import { IJobSimulationGeneration } from '../../models/JobSimulationGeneration';
import logger from '../logger';

export interface SimulationResult {
  success: boolean;
  data?: any;
  error?: string;
}

export const processJobSimulation = async (job: IJobSimulationGeneration): Promise<SimulationResult> => {
  try {
    logger.simple(`Processing simulation for job: ${job.jobId} - ${job.title}`);

    // Simulate processing time (1-3 seconds)
    const processingTime = Math.random() * 2000 + 1000;
    await new Promise(resolve => setTimeout(resolve, processingTime));

    // Simulate success/failure (90% success rate for testing)
    const isSuccess = Math.random() > 0.1;

    if (isSuccess) {
      // Generate mock simulation data
      const simulationData = {
        jobId: job.jobId,
        jobSimulationId: `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        level: 1,
        minute: 30,
        processedAt: new Date().toISOString(),
      };

      logger.simple(`✅ Simulation completed successfully for job: ${job.jobId}`);
      return {
        success: true,
        data: simulationData
      };
    } else {
      const errorMessage = 'Simulation processing failed due to insufficient data';
      logger.simple(`❌ Simulation failed for job: ${job.jobId} - ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    logger.simple(`❌ Error processing simulation for job ${job.jobId}:`, error);
    return {
      success: false,
      error: errorMessage
    };
  }
};
