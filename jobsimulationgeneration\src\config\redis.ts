import { Redis } from 'ioredis';
import logger from '../services/logger';

export const createRedisConnection = (): Redis => {
  const redis = new Redis(process.env.REDIS_URI!);

  redis.on('connect', () => {
    logger.simple('✅ Redis connected successfully');
  });

  redis.on('reconnecting', () => {
    logger.simple('🔄 Redis reconnecting...');
  });

  redis.on('error', (error: any) => {
    logger.simple('❌ Redis connection error:', error);
  });

  redis.on('close', () => {
    logger.simple('⚠️ Redis connection closed');
  });

  return redis;
};
