import axios, { AxiosResponse } from 'axios';
import { IJobSimulationGeneration } from '../models/JobSimulationGeneration';
import logger from './logger';

export interface WebhookPayload {
  jobId: string;
  simulationStatus: 'completed' | 'failed';
  id?: string; // job simulation id
  level?: number;
  minute?: number;
  // simulationData?: any;
  errorMessage?: string;
  processedAt: Date;
  // timestamp: string;
}

export interface WebhookResult {
  success: boolean;
  statusCode?: number;
  error?: string;
  retryCount: number;
}

export const sendWebhook = async (job: IJobSimulationGeneration): Promise<WebhookResult> => {
  const webhookUrl = process.env.WEBHOOK_URL;
  const maxRetries = parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3');
  const retryDelay = parseInt(process.env.WEBHOOK_RETRY_DELAY || '5000');

  if (!webhookUrl) {
    logger.simple('❌ WEBHOOK_URL not configured');
    return {
      success: false,
      error: 'WEBHOOK_URL not configured',
      retryCount: 0
    };
  }

  const payload: WebhookPayload = {
    jobId: job.jobId,
    simulationStatus: job.simulationStatus === 'completed' ? 'completed' : 'failed',
    ...(job.simulationStatus === 'completed' ?
      { id: job.simulationData.jobSimulationId as string, level: job.simulationData.level as number, minute: job.simulationData.minute as number } :
      { errorMessage: job.errorMessage }),
    // simulationData: job.simulationData,
    // errorMessage: job.errorMessage,
    processedAt: job.processedAt || new Date(),
    // timestamp: new Date().toISOString()
  };

  let lastError: string = '';
  let statusCode: number = 0;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.simple(`Webhook: Sending webhook for job ${job.jobId} (attempt ${attempt}/${maxRetries})`);

      const response: AxiosResponse = await axios.post(webhookUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          // 'User-Agent': 'Job-Simulation-Engine/1.0'
        },
        timeout: 10000 // 10 seconds timeout
      });

      statusCode = response.status;

      if (response.status === 200) {
        logger.simple(`Webhook: ✅ sent successfully for job ${job.jobId} on attempt ${attempt}`);
        return {
          success: true,
          statusCode: response.status,
          retryCount: attempt
        };
      } else {
        lastError = `HTTP ${response.status}: ${response.statusText}`;
        logger.simple(`Webhook: ⚠️ returned non-200 status for job ${job.jobId}: ${lastError}`);
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        statusCode = error.response?.status || 0;
        lastError = error.response?.data?.message || error.message;
      } else {
        lastError = error instanceof Error ? error.message : 'Unknown error';
      }

      logger.simple(`Webhook: ❌ attempt ${attempt} failed for job ${job.jobId}:`, lastError);
    }

    // Wait before retry (except for the last attempt)
    if (attempt < maxRetries) {
      logger.simple(`⏳ Waiting ${retryDelay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  logger.simple(`Webhook: ❌ All attempts failed for job ${job.jobId}. Final error: ${lastError}`);
  return {
    success: false,
    statusCode,
    error: lastError,
    retryCount: maxRetries
  };
};
