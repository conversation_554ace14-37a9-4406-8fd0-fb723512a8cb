// Simple test script to verify the API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

// Sample job data
const sampleJobs = [
  {
    id: 'job_001',
    title: 'Senior Software Engineer',
    description: 'We are looking for a senior software engineer with expertise in Node.js and TypeScript.',
    skills: ['Node.js', 'TypeScript', 'MongoDB', 'Express'],
    companyLogo: 'https://example.com/logo1.png',
    companyName: 'Tech Corp'
  },
  {
    id: 'job_002',
    title: 'Frontend Developer',
    description: 'Join our team as a frontend developer working with React and modern web technologies.',
    skills: ['React', 'JavaScript', 'CSS', 'HTML'],
    companyLogo: 'https://example.com/logo2.png',
    companyName: 'Web Solutions Inc'
  },
  {
    id: 'job_003',
    title: 'DevOps Engineer',
    description: 'Looking for a DevOps engineer to manage our cloud infrastructure and CI/CD pipelines.',
    skills: ['A<PERSON>', 'Docker', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>'],
    companyLogo: 'https://example.com/logo3.png',
    companyName: 'Cloud Systems Ltd'
  }
];

async function testAPI() {
  try {
    console.log('🧪 Starting API tests...\n');

    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    // Test 2: Root endpoint
    console.log('2. Testing root endpoint...');
    const rootResponse = await axios.get(`${BASE_URL}/`);
    console.log('✅ Root endpoint:', rootResponse.data);
    console.log('');

    // Test 3: Submit jobs
    console.log('3. Testing job submission...');
    const submitResponse = await axios.post(`${BASE_URL}/api/jobs`, {
      jobs: sampleJobs
    });
    console.log('✅ Job submission:', submitResponse.data);
    console.log('');

    // Test 4: Get jobs list
    console.log('4. Testing jobs list...');
    const jobsResponse = await axios.get(`${BASE_URL}/api/jobs`);
    console.log('✅ Jobs list:', jobsResponse.data);
    console.log('');

    // Test 5: Get specific job
    console.log('5. Testing specific job retrieval...');
    const jobResponse = await axios.get(`${BASE_URL}/api/jobs/job_001`);
    console.log('✅ Specific job:', jobResponse.data);
    console.log('');

    // Test 6: Get stats
    console.log('6. Testing stats endpoint...');
    const statsResponse = await axios.get(`${BASE_URL}/api/stats`);
    console.log('✅ Stats:', statsResponse.data);
    console.log('');

    // Test 7: Get health check
    console.log('7. Testing detailed health check...');
    const detailedHealthResponse = await axios.get(`${BASE_URL}/api/stats/health`);
    console.log('✅ Detailed health check:', detailedHealthResponse.data);
    console.log('');

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    if (error.response) {
      console.error('❌ API Error:', error.response.status, error.response.data);
    } else if (error.request) {
      console.error('❌ Network Error:', error.message);
      console.log('Make sure the server is running on port 3002');
    } else {
      console.error('❌ Error:', error.message);
    }
  }
}

// Run tests
testAPI();
